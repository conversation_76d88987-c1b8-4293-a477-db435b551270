# دليل اختبار المشروع الطبي الذكي

## خطوات الاختبار الشاملة

### 1. اختبار نظام تسجيل الدخول

#### تسجيل حساب جديد:
1. افتح الصفحة الرئيسية: `http://localhost:8000`
2. انقر على زر "تسجيل الدخول"
3. اختر تبويب "إنشاء حساب"
4. أدخل البيانات التالية:
   - الاسم الكامل: أحمد محمد
   - البريد الإلكتروني: <EMAIL>
   - رقم الهاتف: 01234567890
   - كلمة المرور: 123456
   - تأكيد كلمة المرور: 123456
   - ✅ وافق على الشروط والأحكام
5. انقر "إنشاء حساب"
6. **النتيجة المتوقعة**: رسالة نجاح + تسجيل دخول تلقائي + ظهور اسم المستخدم في القائمة

#### تسجيل الدخول:
1. انقر على زر "تسجيل الخروج" من القائمة المنسدلة
2. انقر على "تسجيل الدخول"
3. أدخل البيانات:
   - البريد الإلكتروني: <EMAIL>
   - كلمة المرور: 123456
   - ✅ تذكرني
4. انقر "تسجيل الدخول"
5. **النتيجة المتوقعة**: تسجيل دخول ناجح + ظهور اسم المستخدم

### 2. اختبار نظام التشخيص الذكي

1. انتقل إلى صفحة "التشخيص"
2. اكتب في المحادثة: "أعاني من صداع شديد وحمى منذ يومين"
3. انقر "إرسال"
4. **النتيجة المتوقعة**: رد من النظام مع تحليل الأعراض واقتراح التخصص

### 3. اختبار نظام حجز المواعيد

#### اختيار التخصص:
1. انتقل إلى "حجز موعد"
2. اختر تخصص "أمراض القلب"
3. **النتيجة المتوقعة**: الانتقال لصفحة الأطباء

#### اختيار الطبيب:
1. في صفحة الأطباء، انقر على أي طبيب متاح
2. **النتيجة المتوقعة**: تحديد الطبيب + تفعيل زر "التالي"
3. انقر "التالي"
4. **النتيجة المتوقعة**: الانتقال لصفحة الحجز

#### حجز الموعد:
1. في صفحة الحجز:
   - اختر تاريخ من التقويم
   - اختر وقت من الأوقات المتاحة
   - تأكد من تعبئة بيانات المريض تلقائياً (إذا كنت مسجل دخول)
   - أكمل البيانات المطلوبة:
     - العمر: 30
     - الجنس: ذكر
     - ملاحظات: فحص دوري
2. انقر "تأكيد الحجز"
3. **النتيجة المتوقعة**: الانتقال لصفحة التأكيد مع تفاصيل الموعد

### 4. اختبار إدارة المواعيد

1. انتقل إلى "مواعيدي" من القائمة المنسدلة
2. **النتيجة المتوقعة**: عرض الموعد المحجوز حديثاً
3. جرب الفلاتر:
   - انقر على "القادمة"
   - انقر على "جميع المواعيد"
4. جرب تعديل موعد:
   - انقر "تعديل" على أي موعد
   - اختر تاريخ ووقت جديد
   - انقر "تأكيد التعديل"
5. **النتيجة المتوقعة**: تحديث الموعد بالبيانات الجديدة

### 5. اختبار الوظائف المتقدمة

#### اختبار التصميم المتجاوب:
1. افتح أدوات المطور (F12)
2. فعل وضع الجهاز المحمول
3. جرب أحجام شاشة مختلفة
4. **النتيجة المتوقعة**: التصميم يتكيف مع جميع الأحجام

#### اختبار حفظ البيانات:
1. أغلق المتصفح
2. افتح المتصفح مرة أخرى
3. انتقل للموقع
4. **النتيجة المتوقعة**: بقاء تسجيل الدخول + حفظ المواعيد

#### اختبار الإشعارات:
1. جرب عمليات مختلفة (تسجيل دخول، حجز موعد، إلخ)
2. **النتيجة المتوقعة**: ظهور إشعارات ملونة في أعلى يمين الشاشة

### 6. اختبار معالجة الأخطاء

#### بيانات خاطئة في التسجيل:
1. جرب تسجيل حساب ببريد إلكتروني مستخدم
2. جرب كلمات مرور غير متطابقة
3. جرب بيانات ناقصة
4. **النتيجة المتوقعة**: رسائل خطأ واضحة

#### بيانات خاطئة في تسجيل الدخول:
1. جرب بريد إلكتروني غير موجود
2. جرب كلمة مرور خاطئة
3. **النتيجة المتوقعة**: رسائل خطأ مناسبة

### 7. اختبار الأمان

#### حماية الصفحات:
1. سجل خروج من الحساب
2. حاول الوصول مباشرة لـ: `http://localhost:8000/my-appointments.html`
3. **النتيجة المتوقعة**: إعادة توجيه لصفحة تسجيل الدخول

#### حفظ الجلسة:
1. سجل دخول مع تفعيل "تذكرني"
2. أغلق المتصفح وافتحه مرة أخرى
3. **النتيجة المتوقعة**: بقاء تسجيل الدخول

## نتائج الاختبار المتوقعة

### ✅ الوظائف التي يجب أن تعمل:
- تسجيل الدخول والخروج
- إنشاء حسابات جديدة
- التشخيص الذكي (محادثة تفاعلية)
- حجز المواعيد (التخصص → الطبيب → الحجز → التأكيد)
- عرض وإدارة المواعيد
- تعديل وإلغاء المواعيد
- التصميم المتجاوب
- حفظ البيانات محلياً
- الإشعارات والتنبيهات
- حماية الصفحات

### 🔧 المشاكل المحتملة وحلولها:

#### مشكلة: عدم ظهور الأطباء
**الحل**: تأكد من اختيار التخصص أولاً، أو أعد تحميل الصفحة

#### مشكلة: عدم حفظ البيانات
**الحل**: تأكد من تفعيل JavaScript وعدم استخدام وضع التصفح الخاص

#### مشكلة: مشاكل في التصميم
**الحل**: تأكد من اتصال الإنترنت لتحميل Bootstrap وFont Awesome

#### مشكلة: عدم عمل التشخيص
**الحل**: هذه وظيفة تجريبية، تحتاج لخادم backend حقيقي للعمل الكامل

## ملاحظات مهمة:

1. **البيانات محلية**: جميع البيانات محفوظة في localStorage
2. **التشخيص تجريبي**: نظام التشخيص يعطي ردود تجريبية
3. **الأطباء تجريبيون**: بيانات الأطباء عينات للاختبار
4. **QR Code**: يعمل في صفحة التأكيد
5. **التقويم**: يظهر 14 يوم قادم من اليوم الحالي

---

**إذا واجهت أي مشاكل، تأكد من فتح أدوات المطور (F12) لرؤية رسائل الخطأ**
